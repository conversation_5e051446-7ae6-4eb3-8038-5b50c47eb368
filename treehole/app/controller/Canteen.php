<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\facade\Db;
use app\util\JwtUtil;

class Canteen extends BaseController
{
    // 获取食堂列表
    public function getList()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 获取食堂列表，按评分排序
            $canteens = Db::name('canteens')
                ->where('status', 1)
                ->order('avg_rating desc, total_ratings desc, id asc')
                ->select();

            $result = [];
            foreach ($canteens as $canteen) {
                $floors = json_decode($canteen['floors'], true) ?: [];

                // 获取该食堂下所有窗口的评分和评论数据
                $windows = Db::name('windows')
                    ->where('canteen_id', $canteen['id'])
                    ->where('status', 1)
                    ->select();

                // 计算所有窗口的平均评分和总人数
                $totalWindowRating = 0;
                $totalWindowRatingCount = 0;
                $totalCommentCount = 0;
                $totalRatingCount = 0;
                $hotComment = '';
                $maxLikes = 0;

                foreach ($windows as $window) {
                    // 获取窗口的评分数据
                    $windowRatings = Db::name('window_ratings')
                        ->where('window_id', $window['id'])
                        ->where('status', 1)
                        ->select();

                    $windowRatingCount = count($windowRatings);
                    $totalRatingCount += $windowRatingCount;

                    // 计算窗口平均评分
                    if ($windowRatingCount > 0) {
                        $ratingSum = 0;
                        foreach ($windowRatings as $rating) {
                            $ratingSum += $rating['rating'];
                        }
                        $windowAvgRating = $ratingSum / $windowRatingCount;
                        $totalWindowRating += $windowAvgRating * $windowRatingCount;
                        $totalWindowRatingCount += $windowRatingCount;
                    }

                    // 获取窗口的评论数据（包括回复）
                    $windowComments = Db::name('window_comments')
                        ->where('window_id', $window['id'])
                        ->where('status', 1)
                        ->select();

                    $windowCommentCount = 0;
                    foreach ($windowComments as $comment) {
                        $windowCommentCount++; // 主评论或回复都计数
                    }
                    $totalCommentCount += $windowCommentCount;

                    // 查找点赞数最多的评论作为热评
                    $topComment = Db::name('window_comments')
                        ->where('window_id', $window['id'])
                        ->where('status', 1)
                        ->whereNull('parent_id') // 只查主评论
                        ->order('like_count desc, created_at desc')
                        ->find();

                    if ($topComment && $topComment['like_count'] > $maxLikes) {
                        $maxLikes = $topComment['like_count'];
                        $hotComment = $topComment['content'];
                    }
                }

                // 计算平均评分
                $avgWindowRating = 0;
                if ($totalWindowRatingCount > 0) {
                    $avgWindowRating = round($totalWindowRating / $totalWindowRatingCount, 1);
                }

                // 总人数 = 评分人数 + 评论人数（评论+回复）
                $totalPeople = $totalRatingCount + $totalCommentCount;

                // 如果没有热评，使用食堂描述
                if (!$hotComment) {
                    $hotComment = $canteen['description'] ?: '暂无评论';
                }

                // 计算每层的平均评分和评分数（保留原有逻辑用于其他地方）
                $floorRatings = [];
                foreach ($floors as $floor) {
                    $ratings = Db::name('canteen_ratings')
                        ->where('canteen_id', $canteen['id'])
                        ->where('floor', $floor)
                        ->where('status', 1)
                        ->select();

                    $avgRating = 0;
                    $ratingCount = count($ratings);

                    if ($ratingCount > 0) {
                        $totalRating = 0;
                        foreach ($ratings as $rating) {
                            $totalRating += $rating['rating'];
                        }
                        $avgRating = round($totalRating / $ratingCount, 1);
                    }

                    $floorRatings[] = [
                        'floor' => $floor,
                        'avg_rating' => $avgRating,
                        'rating_count' => $ratingCount
                    ];
                }

                $result[] = [
                    'id' => $canteen['id'],
                    'name' => $canteen['name'],
                    'description' => $hotComment, // 使用点赞数最多的热评
                    'location' => $canteen['location'],
                    'image_url' => $canteen['image_url'],
                    'floors' => $floors,
                    'business_hours' => $canteen['business_hours'],
                    'avg_rating' => $avgWindowRating, // 使用所有窗口的平均评分
                    'total_ratings' => $totalPeople, // 使用评分+评论总人数
                    'ratings' => $floorRatings,
                    'windows' => $windows, // 添加窗口数据供前端使用
                    'rating_count' => $totalRatingCount, // 评分总数
                    'comment_count' => $totalCommentCount, // 评论总数
                    'total_people_count' => $totalPeople // 总人数
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 添加食堂
    public function add()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 验证管理员身份
            $userId = $payload['user_id'];
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user || $user['status'] !== '管理员') {
                return json(['code' => 403, 'msg' => '只有管理员可以发布食堂']);
            }

            $name = request()->post('name');
            $floors = request()->post('floors');
            $description = request()->post('description', '');
            $location = request()->post('location', '');
            $imageUrl = request()->post('image_url', '/images/shitang.png');

            if (!$name || !$floors) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查食堂名称是否已存在
            $existCanteen = Db::name('canteens')
                ->where('name', $name)
                ->where('status', 1)
                ->find();

            if ($existCanteen) {
                return json(['code' => 400, 'msg' => '食堂名称已存在']);
            }

            // 添加食堂
            $canteenData = [
                'name' => $name,
                'description' => $description,
                'location' => $location,
                'image_url' => $imageUrl,
                'floors' => $floors,
                'business_hours' => '07:00-20:00',
                'avg_rating' => 0,
                'total_ratings' => 0,
                'status' => 1,
                'sort_order' => 0
            ];

            $canteenId = Db::name('canteens')->insertGetId($canteenData);

            return json([
                'code' => 200,
                'msg' => '添加成功',
                'data' => ['id' => $canteenId]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 获取食堂详情
    public function getDetail()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $canteenId = request()->post('id');
            if (!$canteenId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 获取食堂信息
            $canteen = Db::name('canteens')
                ->where('id', $canteenId)
                ->where('status', 1)
                ->find();

            if (!$canteen) {
                return json(['code' => 404, 'msg' => '食堂不存在']);
            }

            $floors = json_decode($canteen['floors'], true) ?: [];

            // 获取每层窗口信息
            $windowMap = [];
            foreach ($floors as $floor) {
                $windows = Db::name('windows')
                    ->where('canteen_id', $canteenId)
                    ->where('floor', $floor)
                    ->where('status', 1)
                    ->order('avg_rating desc, total_ratings desc, id asc')
                    ->select();

                $windowList = [];
                foreach ($windows as $window) {
                    // 获取窗口的评论数据（包括回复）
                    $comments = Db::name('window_comments')
                        ->alias('c')
                        ->leftJoin('user u', 'c.user_id = u.id')
                        ->where('c.window_id', $window['id'])
                        ->whereNull('c.parent_id')
                        ->where('c.status', 1)
                        ->field('c.*, u.username, u.face_url as avatar')
                        ->order('c.like_count desc, c.created_at desc')
                        ->select();

                    $commentList = [];
                    $totalCommentCount = 0;
                    $hotComment = '';
                    $maxLikes = 0;

                    // 获取窗口的评分数
                    $ratingCount = Db::name('window_ratings')
                        ->where('window_id', $window['id'])
                        ->where('status', 1)
                        ->count();

                    foreach ($comments as $comment) {
                        $totalCommentCount++; // 主评论计数

                        // 获取回复
                        $replies = Db::name('window_comments')
                            ->alias('r')
                            ->leftJoin('user u', 'r.user_id = u.id')
                            ->where('r.parent_id', $comment['id'])
                            ->where('r.status', 1)
                            ->field('r.*, u.username, u.face_url as avatar')
                            ->order('r.created_at asc')
                            ->select();

                        $replyList = [];
                        foreach ($replies as $reply) {
                            $totalCommentCount++; // 回复计数
                            $replyList[] = [
                                'id' => $reply['id'],
                                'content' => $reply['content'],
                                'time' => $reply['created_at'],
                                'likes' => $reply['like_count'],
                                'images' => $reply['images'] ? json_decode($reply['images'], true) : [],
                                'user' => [
                                    'id' => $reply['user_id'],
                                    'name' => $reply['username'],
                                    'avatar' => $reply['avatar']
                                ]
                            ];
                        }

                        // 选择热评（点赞数最多的评论）
                        if ($comment['like_count'] > $maxLikes) {
                            $maxLikes = $comment['like_count'];
                            $hotComment = $comment['content'];
                            if (strlen($hotComment) > 30) {
                                $hotComment = mb_substr($hotComment, 0, 30) . '...';
                            }
                        }

                        $commentList[] = [
                            'id' => $comment['id'],
                            'content' => $comment['content'],
                            'time' => $comment['created_at'],
                            'likes' => $comment['like_count'],
                            'images' => $comment['images'] ? json_decode($comment['images'], true) : [],
                            'user' => [
                                'id' => $comment['user_id'],
                                'name' => $comment['username'],
                                'avatar' => $comment['avatar']
                            ],
                            'replies' => $replyList
                        ];
                    }

                    // 如果没有热评，使用窗口描述
                    if (!$hotComment) {
                        $hotComment = $window['description'] ?: '暂无评论';
                    }

                    // 总人数 = 评论数 + 回复数 + 评分数
                    $totalPeopleCount = $totalCommentCount + $ratingCount;

                    $windowList[] = [
                        'id' => $window['id'],
                        'name' => $window['name'],
                        'img' => $window['image_url'] ?: '/images/shitang.png',
                        'total_rating' => (float)$window['avg_rating'],
                        'hot_comment' => $hotComment,
                        'score_count' => $totalPeopleCount,
                        'specialty' => $window['specialty'] ?: '美食',
                        'description' => $window['description'] ?: '',
                        'comments' => $commentList,
                        'rating_count' => $ratingCount,
                        'comment_count' => $totalCommentCount,
                        'total_people_count' => $totalPeopleCount
                    ];
                }
                $windowMap[$floor] = $windowList;
            }

            $result = [
                'id' => $canteen['id'],
                'name' => $canteen['name'],
                'description' => $canteen['description'],
                'location' => $canteen['location'],
                'image_url' => $canteen['image_url'],
                'floors' => $floors,
                'business_hours' => $canteen['business_hours'],
                'avg_rating' => (float)$canteen['avg_rating'],
                'total_ratings' => $canteen['total_ratings'],
                'window_map' => $windowMap
            ];

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 添加评分
    public function addRating()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];
            $canteenId = request()->post('canteen_id');
            $floor = request()->post('floor');
            $rating = request()->post('rating');
            $comment = request()->post('comment', '');
            $images = request()->post('images', '');

            if (!$canteenId || !$floor || !$rating) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            if ($rating < 1 || $rating > 5) {
                return json(['code' => 400, 'msg' => '评分范围为1-5分']);
            }

            // 检查是否已经评分过
            $existRating = Db::name('canteen_ratings')
                ->where('canteen_id', $canteenId)
                ->where('user_id', $userId)
                ->where('floor', $floor)
                ->where('status', 1)
                ->find();

            if ($existRating) {
                return json(['code' => 400, 'msg' => '您已经对该楼层评分过了']);
            }

            // 添加评分
            $ratingData = [
                'canteen_id' => $canteenId,
                'user_id' => $userId,
                'floor' => $floor,
                'rating' => $rating,
                'comment' => $comment,
                'images' => $images
            ];

            Db::name('canteen_ratings')->insert($ratingData);

            // 更新食堂平均评分
            $this->updateCanteenRating($canteenId);

            return json([
                'code' => 200,
                'msg' => '评分成功'
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新食堂平均评分
     */
    private function updateCanteenRating($canteenId)
    {
        $ratings = Db::name('canteen_ratings')
            ->where('canteen_id', $canteenId)
            ->where('status', 1)
            ->select();

        $totalRatings = count($ratings);
        $avgRating = 0;

        if ($totalRatings > 0) {
            $totalScore = 0;
            foreach ($ratings as $rating) {
                $totalScore += $rating['rating'];
            }
            $avgRating = round($totalScore / $totalRatings, 2);
        }

        Db::name('canteens')->where('id', $canteenId)->update([
            'avg_rating' => $avgRating,
            'total_ratings' => $totalRatings
        ]);
    }


}