// pages/home/<USER>
import { setWatcher } from '../../../watch.js';
import eventBus from '../../../utils/eventBus';
// 引入角色管理器
import roleManager from '../../../utils/roleManager';

Page({
  data: {
    titlecolor: '0',
    unread_id: '',
    value: '',
    messages1: [], // 存储交易市场的消息数据
    messages2: [], // 存储告白倾诉的消息数据
    messages3: [], // 存储校园吐槽的消息数据
    messages4: [], // 存储提问求助的消息数据
    messages99: [], // 存储提问求助的消息数据
    page1: 1, // 当前页码
    page2: 1,
    page3: 1,
    page4: 1,
    page99: 1,
    isLoading: false, // 是否正在加载数据
    selectedItemId: 1,
    abc: [254, 251, 229],
    showGuideHint: false, // 是否显示引导提示
    guideStep: 1, // 当前引导步骤
    totalGuideSteps: 5, // 总引导步骤数
    guideClosing: false, // 引导是否正在关闭
    grid: [
      { number: 1, text: "全部消息", icon: "/images/pinglun.png" },
      { number: 2, text: "发条说说", icon: "/images/dangshidati-01.png" },
      { number: 3, text: "校园交易", icon: "/images/ershouwupin.png" }
    ],
    showUpdateNotice: false, // 是否显示更新公告
    noticeClosing: false, // 更新公告是否正在关闭
    updateNoticeAnimation: {}, // 在 data 中添加动画属性
    showPublishPopup: false, // 新增发布弹窗状态
    hotTopics: [],
    currentHotIndex: 0,
    hotTopicTimer: null,
    showHotTopicModal: false,
    showHotTopic: true,
    displayTopics: [], // 新增：用于存储当前显示的两条消息
  },

  onLoad(options) {
    // 初始化watch配置
    this.setWatchConfig();
    
    setWatcher(this);
    this.getGridConfig();
    this.setData({
      page1: 1,
      page2: 1,
      page3: 1,
      page4: 1,
      page99: 1,
      messages1: [],
      messages2: [],
      messages3: [],
      messages4: [],
      messages99: [],
    });
    this.loadMessages();
    
    // 检查登录状态并处理引导
    this.checkLoginAndGuide();
    
    // 监听点赞状态变化
    eventBus.on('likeStatusChanged', this.handleLikeStatusChanged);
    eventBus.on('commentLikeStatusChanged', this.handleCommentLikeStatusChanged);
    
    // 获取热帖数据
    this.getHotTopics();
    
    // 启动热帖自动滚动
    this.startHotTopicScroll();

    const showHotTopic = wx.getStorageSync('showHotTopic');
    if (showHotTopic !== "") {
      this.setData({ showHotTopic });
    } else {
      this.getHotTopicSetting();
    }
  },

  // 将watch配置移到单独的方法中
  setWatchConfig() {
    this.watch = {
      selectedItemId: function(newVal) {
        this.setData({
          page1: 1,
          page2: 1,
          page3: 1,
          page4: 1,
          page99: 1,
          messages1: [],
          messages2: [],
          messages3: [],
          messages4: [],
          messages99: [],
        });
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });
        this.loadMessages();
      }
    };
  },

  viewMessageDetail: function(e) {
    const index = e.currentTarget.dataset.index;
    const selectedItemId = this.data.selectedItemId;
    const selectedMessage = this.data[`messages${selectedItemId}`][index];
    wx.navigateTo({
      url: `/packageEmoji/pages/messageDetail/messageDetail?id=${selectedMessage.id}`
    });
  },

  loadMessages(callback) {
    if (this.data.isLoading) return;

    const { selectedItemId } = this.data;
    let pageKey = `page${selectedItemId}`;
    let messagesKey = `messages${selectedItemId}`;

    // 只在第一次加载时显示loading
    if (this.data[pageKey] === 1) {
      this.setData({ isLoading: true });
    }

    wx.request({
      url: getApp().globalData.wangz + '/message/getMessages',
      method: 'POST',
      data: {
        choose: selectedItemId,
        page: this.data[pageKey],
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        let newMessages = res.data.data || [];
        
        newMessages = newMessages.map(item => {
          if (typeof item.images === 'string') {
            try {
              item.images = JSON.parse(item.images.replace(/\\/g, ''));
            } catch (error) {
              console.error("Failed to parse JSON in images:", error, item.images);
              item.images = [];
            }
          }

          for (let key in item) {
            if (item[key] === null) {
              item[key] = '';
            }
          }

          return item;
        });

        const newPage = this.data[pageKey] + 1;
        this.setData({
          [messagesKey]: [...this.data[messagesKey], ...newMessages],
          [pageKey]: newPage,
          isLoading: false
        });
        if (callback) callback();
      },
      fail: (error) => {
        this.setData({ isLoading: false });
        wx.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
        console.log('请求失败', error);
        if (callback) callback();
      },
    });
  },

  selectItem(event) {
    const itemId = event.currentTarget.dataset.number;
    this.setData({
      selectedItemId: itemId,
      page1: 1,
      page2: 1,
      page3: 1,
      page4: 1,
      page99: 1,
      messages1: [],
      messages2: [],
      messages3: [],
      messages4: [],
      messages99: [],
    });
    this.loadMessages();
  },

  scrollToTop() {
    this.setData({
      page1: 1,
      page2: 1,
      page3: 1,
      page4: 1,
      page99: 1,
      messages1: [],
      messages2: [],
      messages3: [],
      messages4: [],
      messages99: [],
    });
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
    this.loadMessages();
  },

  publish() {
    if (roleManager.hasBasePermission()) {
      // 显示发布弹窗
      this.setData({
        showPublishPopup: true
      });
    } else {
      wx.showModal({
        title: '提示',
        content: '完成学生认证才能发帖',
        confirmText: '去认证',
        cancelText: '取消',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/fold3/student/student'
            });
          }
        }
      });
    }
  },

  // 关闭发布弹窗
  closePublishPopup() {
    this.setData({
      showPublishPopup: false
    });
  },

  vxgroup() {
    wx.navigateTo({
      url: '/pages/foldshare/vxgroup/vxgroup'
    });
  },

  onReady() {},

  onShow() {
    // 检查是否有新通知
    if (getApp().globalData.user_id) {
      getApp().globalData.updateAllNotifications();
      
      // 检查更新公告
      if (getApp().globalData.isLoggedIn) {
        this.checkUpdateNotice();
      }
    }
    
    // 重新获取热榜状态
    const showHotTopic = wx.getStorageSync('showHotTopic');
    if (showHotTopic !== "") {
      // 如果本地存储中的状态与当前状态不同，则更新
      if (this.data.showHotTopic !== showHotTopic) {
        this.refreshHotTopic(showHotTopic);
      }
    } else {
      // 如果本地没有存储，则使用全局配置
      const globalHotTopic = getApp().globalData.showHotTopic;
      if (this.data.showHotTopic !== globalHotTopic) {
        this.refreshHotTopic(globalHotTopic);
      }
    }
    
    // 切换到校园生活页时检查新活动
    getApp().globalData.checkNewActivities();
    // 更新所有通知
    getApp().globalData.updateAllNotifications();
    // 更新未读消息数Badge
    const app = getApp();
    if (app.globalData.websocket) {
      app.globalData.websocket.updateBadgeOnShow();
    }
    
    this.getGridConfig();
  },

  onHide() {},

  onUnload() {
    if (this.fireworksTimer) {
      clearInterval(this.fireworksTimer);
    }
    if (this.checkLoginInterval) {
      clearInterval(this.checkLoginInterval);
    }
    
    // 页面卸载时取消监听
    eventBus.off('likeStatusChanged', this.handleLikeStatusChanged);
    eventBus.off('commentLikeStatusChanged', this.handleCommentLikeStatusChanged);
    
    // 清除热帖定时器
    if (this.data.hotTopicTimer) {
      clearInterval(this.data.hotTopicTimer);
    }
  },

  onPullDownRefresh() {
    this.setData({
      page1: 1,
      page2: 1,
      page3: 1,
      page4: 1,
      page99: 1,
      messages1: [],
      messages2: [],
      messages3: [],
      messages4: [],
      messages99: [],
    });

    this.loadMessages(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    this.loadMessages();
  },

  dolike: function (e) {
    const that = this;
    const userId = getApp().globalData.user_id;
    const messageId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    if (!userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: getApp().globalData.wangz + '/like/doLike',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: {
        message_id: messageId
      },
      success(res) {
        if (res.data.error_code === 0) {
          // 更新本地点赞状态
          const messagesKey = `messages${that.data.selectedItemId}`;
          const messages = that.data[messagesKey];
          messages[index].is_liked = res.data.data.is_liked;
          messages[index].total_likes = res.data.data.total_likes;
          
          that.setData({
            [messagesKey]: messages
          });

          // 发送点赞状态更新事件
          const app = getApp();
          if (app.globalData.websocket) {
            app.globalData.websocket.sendLike(
              messageId,
              'message',
              res.data.data.is_liked ? 'like' : 'unlike',
              messages[index].user_id
            ).catch(error => {
              // 移除console.error调用
            });
          }

          wx.showToast({
            title: res.data.data.is_liked ? '点赞成功(*´∀`)~♥' : '取消点赞(⁰﹏⁰)',
            icon: 'none',
            duration: 750
          });
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          });
        }
      },
      fail() {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  onShareAppMessage() {},

  // 获取grid配置
  getGridConfig() {
    wx.request({
      url: getApp().globalData.wangz + '/message/getGridConfig',
      method: 'POST',
      data: {
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.status === 1) {
          let currentGrid = this.data.grid;
          // 根据后端返回的配置添加或移除选项
          if (res.data.show_extra_items) {
            if (!currentGrid.find(item => item.number === 4)) {
              currentGrid.push({ number: 4, text: "告白倾诉", icon: "/images/收藏.png" });
            }
            if (!currentGrid.find(item => item.number === 99)) {
              currentGrid.push({ number: 99, text: "寻找搭子", icon: "/images/cansaitubiaozhuanqu-.png" });
            }
          } else {
            // 如果不显示额外选项，则移除它们
            currentGrid = currentGrid.filter(item => item.number !== 4 && item.number !== 99);
          }
          this.setData({
            grid: currentGrid
          });
        }
      }
    });
  },

  // 检查登录状态并处理引导
  checkLoginAndGuide() {
    const app = getApp();
    // 检查是否已经登录
    if (app.globalData.isLogin) {
      console.log('用户已登录，直接检查引导状态');
      this.checkGuideStatusAfterLogin();
    } else {
      console.log('用户未登录，等待登录...');
      // 设置轮询检查登录状态
      this.checkLoginInterval = setInterval(() => {
        const user_id = wx.getStorageSync('user_id');
        if (user_id) {
          console.log('检测到用户已登录，开始检查引导状态');
          clearInterval(this.checkLoginInterval);
          this.checkGuideStatusAfterLogin();
        }
      }, 1000); // 每秒检查一次

      // 设置超时，避免无限轮询
      setTimeout(() => {
        if (this.checkLoginInterval) {
          clearInterval(this.checkLoginInterval);
          console.log('登录检查超时');
        }
      }, 10000); // 10秒后停止检查
    }
  },

  // 登录后检查引导状态
  checkGuideStatusAfterLogin() {
    const user_id = wx.getStorageSync('user_id');
    if (!user_id) {
      console.error('用户ID不存在');
      return;
    }

    console.log('开始检查引导状态，用户ID:', user_id);
    // 先检查新手引导状态
    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'guide'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        console.log('引导状态检查返回:', res.data);
        if (res.data.code === 1) {
          if (res.data.data && res.data.data.should_show_hint) {
            console.log('需要显示引导');
            // 如果需要显示引导
            this.setData({
              showGuideHint: true,
              guideStep: 1,
              showUpdateNotice: false,
              noticeClosing: false,
              guideClosing: false
            });
          } else {
            console.log('不需要显示引导，检查更新公告');
            // 如果引导已完成，检查更新公告
            this.checkUpdateNotice();
          }
        } else {
          console.error('引导状态检查返回错误:', res.data);
        }
      },
      fail: (error) => {
        console.error('引导状态检查请求失败:', error);
      }
    });
  },

  // 检查更新公告状态
  checkUpdateNotice() {
    const user_id = wx.getStorageSync('user_id');
    if (!user_id) {
      console.error('用户ID不存在');
      return;
    }
    
    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'notice'  // 改回正确的 step_key
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1 && res.data.data.should_show_hint) {
          this.setData({
            showUpdateNotice: true,
            showGuideHint: false,
            guideClosing: false,
            noticeClosing: false
          });
        }
      },
      fail: (error) => {
        console.error('更新公告检查失败:', error);
      }
    });
  },

  // 显示引导提示
  showGuide() {
    this.setData({
      showUpdateNotice: true,
      showGuideHint: false,
      guideClosing: false,
      noticeClosing: false
    });
  },

  // 下一步引导
  nextGuideStep() {
    const nextStep = this.data.guideStep + 1;
    this.setData({
      guideStep: nextStep
    });
  },

  // 跳过引导
  skipGuide() {
    const user_id = wx.getStorageSync('user_id');
    
    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/updateHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'guide'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          this.setData({
            showGuideHint: false,
            guideClosing: false
          });
          
          // 检查更新公告状态
          wx.request({
            url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
            method: 'POST',
            data: {
              user_id: user_id,
              page_key: 'home_page',
              step_key: 'notice'
            },
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            success: (noticeRes) => {
              if (noticeRes.data.code === 1 && noticeRes.data.data.should_show_hint) {
                setTimeout(() => {
                  this.setData({
                    showUpdateNotice: true
                  });
                }, 500); // 等待引导关闭动画完成后再显示更新公告
              }
            }
          });
        }
      }
    });
  },

  // 关闭引导提示
  closeGuideHint() {
    const user_id = wx.getStorageSync('user_id');
    
    this.setData({
      guideClosing: true
    });

    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/updateHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'guide'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          // 检查更新公告状态
          wx.request({
            url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
            method: 'POST',
            data: {
              user_id: user_id,
              page_key: 'home_page',
              step_key: 'notice'
            },
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            success: (noticeRes) => {
              this.setData({
                showGuideHint: false,
                guideClosing: false
              });
              
              // 如果需要显示更新公告
              if (noticeRes.data.code === 1 && noticeRes.data.data.should_show_hint) {
                setTimeout(() => {
                  this.setData({
                    showUpdateNotice: true
                  });
                }, 500); // 等待引导关闭动画完成后再显示更新公告
              } else {
                this.setData({
                  showUpdateNotice: false
                });
              }
            },
            fail: () => {
              this.setData({
                showGuideHint: false,
                guideClosing: false,
                showUpdateNotice: false
              });
            }
          });
        }
      },
      fail: () => {
        this.setData({
          showGuideHint: false,
          guideClosing: false,
          showUpdateNotice: false
        });
      }
    });
  },

  // 关闭更新公告
  closeUpdateNotice() {
    const user_id = wx.getStorageSync('user_id');
    if (!user_id) {
      console.error('用户ID不存在');
      return;
    }
    
    this.setData({
      noticeClosing: true
    });

    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/updateHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'notice'  // 改回正确的 step_key
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          this.setData({
            showUpdateNotice: false,
            noticeClosing: false
          });
        } else {
          console.error('更新公告状态更新失败:', res.data);
          // 即使更新失败也关闭弹窗
          this.setData({
            showUpdateNotice: false,
            noticeClosing: false
          });
        }
      },
      fail: (error) => {
        console.error('更新公告状态更新请求失败:', error);
        this.setData({
          showUpdateNotice: false,
          noticeClosing: false
        });
      }
    });
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件冒泡
  },

  onInput: function (e) {
    this.setData({
      value: e.detail.value
    });
  },

  onSearch: function (e) {
    const query = e.detail.value || e.detail; // 处理不同的事件结构
    if (query) {
      this.setData({ isLoading: true });
      wx.request({
        url: getApp().globalData.wangz + '/message/searchMessages',
        method: 'POST',
        data: {
          keyword: query,
          page: 1 // 初始为第一页
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        },
        success: (res) => {
          this.setData({ isLoading: false });
          if (res.statusCode === 200 && res.data && res.data.data) {
            const results = encodeURIComponent(JSON.stringify(res.data.data));
            const keyword = encodeURIComponent(query); // 对搜索词进行编码
            wx.navigateTo({
              url: `/pages/foldshare/results/results?messages=${results}&keyword=${keyword}`
            });
          } else {
            wx.showToast({
              title: '没有找到相关结果或接口错误',
              icon: 'none'
            });
          }
        },
        fail: () => {
          this.setData({ isLoading: false });
          wx.showToast({
            title: '搜索失败，请重试',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '请输入搜索内容',
        icon: 'none'
      });
    }
  },

  // 处理点赞状态变化
  handleLikeStatusChanged: function(data) {
    const { messageId, isLiked, totalLikes } = data;
    const messagesKey = `messages${this.data.selectedItemId}`;
    const messages = this.data[messagesKey];
    if (!messages) return;
    
    const updatedMessages = messages.map(message => {
      if (message.id === messageId) {
        return {
          ...message,
          is_liked: isLiked,
          total_likes: totalLikes
        };
      }
      return message;
    });
    
    this.setData({
      [messagesKey]: updatedMessages
    });
  },

  // 处理评论点赞状态变化
  handleCommentLikeStatusChanged: function(data) {
    // 如果需要更新评论的点赞状态，在这里实现
  },

  goToGongju() {
    this.closeUpdateNotice(); // 先关闭更新公告
    wx.switchTab({
      url: '/pages/fold4/gongju/gongju',
      success: () => {
        console.log('跳转成功');
      },
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.navigateTo({
          url: '/pages/fold4/gongju/gongju',
          fail: (err) => {
            console.error('navigateTo也失败:', err);
            wx.showToast({
              title: '跳转失败，请重试',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  // 添加热门按钮点击事件处理函数
  goToHot() {
    wx.navigateTo({
      url: '/pages/fold1/hot/hot'
    });
  },

  // 获取热帖数据
  getHotTopics() {
    wx.request({
      url: getApp().globalData.wangz + '/message/getHotMessages',
      method: 'POST',
      data: {
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          const hotTopics = res.data.data.map(item => item.content || '');

          // 使用相同的处理函数来处理初始显示
          const processTopic = (topic, index) => {
            // 严格获取第一行内容，遇到任何换行符都截断
            const firstLine = topic.split(/[\n\r\u2028\u2029]/)[0].trim();
            // 移除所有可能的换行符和空白字符
            const cleanContent = firstLine.replace(/[\n\r\u2028\u2029]/g, '').trim();

            // 如果原文本包含换行符，直接截断不显示后续内容
            const hasLineBreak = /[\n\r\u2028\u2029]/.test(topic);

            if (hasLineBreak) {
              // 有换行符时，直接使用第一行内容，不管长度
              return {
                content: cleanContent,
                rank: index + 1
              };
            } else {
              // 没有换行符时，按原逻辑限制30个字符
              return {
                content: cleanContent.length > 30 ? cleanContent.substring(0, 30) + '...' : cleanContent,
                rank: index + 1
              };
            }
          };

          // 初始显示第1和第2条，设置currentHotIndex为0，但第一次滚动时直接跳到下一组
          const initialDisplayTopics = hotTopics.slice(0, 2).map((topic, index) =>
            processTopic(topic, index)
          );

          this.setData({
            hotTopics: hotTopics,
            currentHotIndex: 0, // 从0开始
            displayTopics: initialDisplayTopics
          });
        }
      }
    });
  },

  // 启动热帖自动滚动
  startHotTopicScroll() {
    if (this.data.hotTopicTimer) {
      clearInterval(this.data.hotTopicTimer);
    }

    const timer = setInterval(() => {
      if (!this.data.hotTopics || this.data.hotTopics.length === 0) {
        return;
      }

      // 直接移动到下一个位置
      const nextIndex = (this.data.currentHotIndex + 1) % this.data.hotTopics.length;
      const followingIndex = (nextIndex + 1) % this.data.hotTopics.length;

      // 获取要显示的两条热榜内容
      const firstTopic = this.data.hotTopics[nextIndex] || '';
      const secondTopic = this.data.hotTopics[followingIndex] || '';

      // 处理换行符，只显示第一行
      const processTopic = (topic, actualIndex) => {
        // 严格获取第一行内容，遇到任何换行符都截断
        const firstLine = topic.split(/[\n\r\u2028\u2029]/)[0].trim();
        // 移除所有可能的换行符和空白字符
        const cleanContent = firstLine.replace(/[\n\r\u2028\u2029]/g, '').trim();

        // 如果原文本包含换行符，直接截断不显示后续内容
        const hasLineBreak = /[\n\r\u2028\u2029]/.test(topic);

        if (hasLineBreak) {
          // 有换行符时，直接使用第一行内容，不管长度
          return {
            content: cleanContent,
            rank: actualIndex + 1
          };
        } else {
          // 没有换行符时，按原逻辑限制30个字符
          return {
            content: cleanContent.length > 30 ? cleanContent.substring(0, 30) + '...' : cleanContent,
            rank: actualIndex + 1
          };
        }
      };

      // 更新显示的两条消息
      const displayTopics = [
        processTopic(firstTopic, nextIndex),
        processTopic(secondTopic, followingIndex)
      ];

      this.setData({
        currentHotIndex: nextIndex,
        displayTopics: displayTopics
      });
    }, 3000); // 每3秒滚动一次

    this.setData({ hotTopicTimer: timer });
  },

  showHotTopicModal(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    this.setData({
      showHotTopicModal: true
    });
  },

  closeHotTopicModal() {
    this.setData({
      showHotTopicModal: false
    });
  },

  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  },

  getHotTopicSetting() {
    const userId = getApp().globalData.user_id;
    if (!userId) {
      this.setData({ showHotTopic: true }); // 默认显示
      return;
    }
    
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/getButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'hot_topic'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          this.setData({ showHotTopic: res.data.data.button_status === '1' });
          wx.setStorageSync('showHotTopic', res.data.data.button_status === '1');
        }
      }
    });
  },
  
  // 添加刷新热榜状态的方法
  refreshHotTopic(status) {
    // 如果状态未定义，从本地存储获取
    if (status === undefined) {
      status = wx.getStorageSync('showHotTopic');
    }
    
    // 更新显示状态
    this.setData({ showHotTopic: status });
    
    // 如果是关闭热榜，同时关闭热榜弹窗
    if (status === false && this.data.showHotTopicModal) {
      this.closeHotTopicModal();
    }
    
    // 如果是开启热榜但没有热榜数据，重新获取数据
    if (status === true && (!this.data.hotTopics || this.data.hotTopics.length === 0)) {
      this.getHotTopics();
    }
  },
});