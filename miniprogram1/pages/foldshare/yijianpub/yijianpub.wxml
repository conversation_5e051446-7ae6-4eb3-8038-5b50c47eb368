<!--pages/foldshare/yijianpub/yijianpub.wxml-->
<van-nav-bar class="navsetting" fixed="true" placeholder="true" bind:click-left="onClickLeft">
  <text class="navtext" slot="right">意见建议</text>
  <view slot="left">
    <image src="/images/chexiao.png" style="width: 60rpx; height: 55rpx; margin-top: 30rpx;" />
  </view>
</van-nav-bar>
<view class="fixed-button2" bindtap="publish" style="{{isPublishing ? 'opacity:0.5;pointer-events:none;' : ''}} {{showModal ? 'opacity:0;pointer-events:none;' : ''}}">
  <image class="custom-icon" src="/images/pub.jpeg"></image>
</view>
<view class="gradient-background3">
  <!-- 联系方式弹窗 - 从下方弹出 -->
  <view class="contact-modal {{showModal ? 'contact-modal-show' : ''}}" catchtap="hideModal" catchtouchmove="preventTouchMove">
    <view class="contact-modal-content" catchtap="stopPropagation">
      <view class="contact-modal-header">
        <view class="contact-modal-title">联系方式</view>
        <view class="contact-modal-close" bindtap="hideModal">
          <image src="/images/guanbi.png" style="width: 30rpx; height: 30rpx;"></image>
        </view>
      </view>

      <view class="contact-input-container">
        <input type="text" placeholder="请填写微信号" class="contact-input" model:value="{{vx}}" />
      </view>

      <view class="contact-input-container">
        <input type="text" placeholder="请填写QQ号" class="contact-input" model:value="{{qq}}" />
      </view>

      <view class="contact-input-container">
        <input type="text" placeholder="请填写手机号" class="contact-input" model:value="{{phonenumber}}" />
      </view>

      <!-- 提示文字和填写完毕按钮 -->
      <view class="contact-modal-footer">
        <view class="contact-tip">联系方式填写任意个均可</view>
        <view class="contact-confirm-btn" bindtap="hideModal">
          填写完毕
        </view>
      </view>
    </view>
  </view>
  <!-- 框 -->
  <view class="bgkuang">
    <view class="textarea-container">
      <textarea value="{{ value }}" placeholder="每条意见我都会回复的！" bindinput="onChange1" style="width: 100%; padding:0 50rpx; box-sizing: border-box;" maxlength="2000" auto-height="true" />
    </view>
    <view class="upload-container">
      <block wx:for="{{images.length < 9 ? images.length + 1 : 9}}" wx:key="index">
        <view class="image-item">
          <!-- 已上传的图片 -->
          <image wx:if="{{index < images.length}}" src="{{images[index]}}" mode="aspectFill" class="image" bindtap="previewImage" data-index="{{index}}" />
          <!-- 添加图片的按钮 -->
          <view wx:elif="{{index == images.length && images.length < 9}}" class="add-icon" bindtap="chooseImage">
            <text style="font-size: 100rpx;">+</text>
          </view>
        </view>
      </block>
    </view>
  </view>
  <view class="profile-container2">
    <view class="label">联系方式</view>
    <!-- 使用 button 包裹 image -->
    <button bind:tap="dianji5" class="avatar-button">
      <text class="label2">{{contactresult}}</text>
      <image class="avatar" src="/images/xiangxiajiantou.png" mode="heightFix" />
    </button>
  </view>
  <view class="profile-container2">
    <view class="label">匿名发布</view>
    <view class="ios-switch {{isAnonymous ? 'enabled' : ''}}" bindtap="toggleAnonymous">
      <view class="switch-handle"></view>
    </view>
  </view>
</view>