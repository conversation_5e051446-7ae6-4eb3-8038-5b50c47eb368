/* pages/foldshare/yijianpub/yijianpub.wxss */
.navsetting{
  --nav-bar-background-color: rgb(245, 245, 245);
}
.gradient-background3 {
  padding-top: 50rpx;
  min-height: 100vh; 
  height: auto;
  background-color: rgb(245, 245, 245);
}
.navtext{
  margin-bottom: 10rpx;
  font-family: '阿里妈妈刀隶体 Regular';
  font-size: 62rpx;
  margin-right: 226rpx; /* 这里留下原来的值，因为是导航栏上的意见建议四个字 */
}

.custom-icon{
  width: 80rpx;
 height: 80rpx;
 margin-right: 5px;
 border-radius: 50%;
}
.fixed-button2 {
  position: fixed;
  bottom: 200rpx;
  right: 20rpx;
  color: white;
  background-color:rgb(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 80rpx; 
  height: 80rpx;
  z-index: 1000;
}
.radio-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.radio-item {
  width: 30%; /* 宽度设置为父容器的 30% 使其在一行显示 3 个 */
  margin-bottom: 10px;
}
.textarea-container{
  margin-bottom: 80rpx;
}
.upload-container {
  display: flex;
  flex-wrap: wrap;
  padding-left: 40rpx; /* 左侧间隔 */
  padding-right: 40rpx; /* 右侧间隔 */
}

.image-item {
  width: calc(33.33% - 20rpx); /* 每个图片的宽度减去间隔的一半 */
  padding-bottom: calc(33.33% - 20rpx); /* 确保 1:1 的长宽比 */
  position: relative;
  margin-right: 20rpx; /* 图片之间的水平间隔 */
  margin-bottom: 20rpx; /* 图片之间的垂直间隔 */
  border-radius: 13rpx;
  overflow: hidden;
  box-sizing: border-box;
}

.image-item:nth-child(3n) {
  margin-right: 0; /* 每行最后一张图片不加水平间隔 */
}

.image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.placeholder {
  background-color: #f0f0f0;
}

.add-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: rgb(188, 188, 188);
  background-color: rgb(235, 235, 235);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 13rpx;
}
.dropdown {
  margin: 0 auto;
  position: relative;
  width: 90%;
}


.dropdown-bar {
  justify-content: center; /* 水平居中 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  padding: 10rpx;
  border-radius: 5rpx;
  background-color: rgb(245, 245, 245);
}

.dropdown-label {
  flex-basis: 25%;
  text-align: left;
}

.selected-item {
  flex-grow: 1;
  text-align: right;
  padding-right: 20rpx;
}

.dropdown-button {
  background: none;
  border: none;
  font-size: 30rpx;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: rgb(245, 245, 245);
  z-index: 10;
  border-radius: 5rpx;
}
.input-field {
  text-align: right;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx;
  border-radius: 5rpx;
  background-color: rgb(245, 245, 245);
}
.bgkuang{
  padding-top: 40rpx;
  padding-bottom: 20rpx;
  height: auto;
  background-color: rgb(255, 255, 255);
  margin-right: 40rpx;
  margin-left: 40rpx;
  border-radius:20rpx;
  margin-bottom: 40rpx;
}
.label {
  flex-shrink: 0;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
}
.profile-container2 {
  height: 100rpx;
  margin: 20rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.input-right {
  font-size: 28rpx;
  text-align: right;
  color: #464141;
}
.avatar {
  width: 36rpx;
  height: 36rpx;
  margin-left: 10rpx;
}
.avatar-button:active .avatar {
  transform: scale(1.1); /* 点击时头像放大 */
  transition: transform 0.2s;
}
.avatar-button {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-grow: 1;
  width: auto;
  height: 60rpx;
  border: none;
  padding: 0;
  margin: 0;
  background-color: transparent;
}
.avatar-button::after {
  border: none;
}
.label2 {
  font-size: 28rpx;
  color: #999;
}
.modal2 {
  z-index: 1001;
  box-sizing: border-box;
  padding-left: 20rpx;
  padding-right: 20rpx;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color:rgb(240, 240, 240);
  transition: height 0.3s;
  overflow: hidden;
  border-top-left-radius: 40rpx; /* 左上角圆角 */
  border-top-right-radius: 40rpx; /* 右上角圆角 */
}
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  backdrop-filter: blur(4px);
}

.modal {
  padding-bottom: 30rpx;
  position: fixed;
  left: 50%;
  top: 50%;
  background-color: #ffffff;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 999;
  transform: translate(-50%, -50%);
  animation: modalShow 0.3s ease forwards;
  width: 80%; /* 添加固定宽度 */
  max-width: 600rpx; /* 最大宽度 */
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: translate(-50%, -45%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.modal-header3 {
  margin-top: 0;
  text-align: center;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
  padding: 30rpx 20rpx;
  position: relative;
  overflow: hidden;
}

.modal-header3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  background: #ffffff;
  border-radius: 50% 50% 0 0;
}

.modal-header4 {
  position: relative;
  z-index: 1;
  text-align: center;
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
}

.profile-container2:active {
  background-color: #f8f9fa;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1001;
  width: 64rpx;
  height: 64rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.modal-close::before,
.modal-close::after {
  content: '';
  position: absolute;
  width: 28rpx;
  height: 4rpx;
  background-color: #ffffff;
  border-radius: 2rpx;
}

.modal-close::before {
  transform: rotate(45deg);
}

.modal-close::after {
  transform: rotate(-45deg);
}

/* IOS开关样式 */
.ios-switch {
  width: 80rpx;
  height: 44rpx;
  border-radius: 22rpx;
  background-color: #e5e5e5;
  position: relative;
  transition: background-color 0.3s;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.ios-switch.enabled {
  background-color: #07c160;
}

.switch-handle {
  width: 40rpx;
  height: 40rpx;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: left 0.3s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.ios-switch.enabled .switch-handle {
  left: 38rpx;
}

/* 投票面板 */
.vote-panel {
  margin: 0 50rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s, padding 0.5s;
}

.vote-panel-show {
  max-height: 1000rpx;
  padding: 30rpx;
}

.vote-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.vote-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.vote-type-switch {
  display: flex;
  border: 1px solid #07c160;
  border-radius: 8rpx;
  overflow: hidden;
}

.vote-type-switch view {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #07c160;
  background-color: white;
}

.vote-type-switch view.active {
  color: white;
  background-color: #07c160;
}

.vote-options {
  margin-bottom: 20rpx;
}

.vote-option {
  margin-bottom: 15rpx;
}

.vote-option-content {
  display: flex;
  align-items: center;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 15rpx;
}

.vote-input {
  flex: 1;
  border: none;
  font-size: 26rpx;
}

.delete-icon {
  color: #999;
  font-size: 36rpx;
  padding: 0 10rpx;
}

.add-option {
  padding: 15rpx 0;
  text-align: center;
  color: #07c160;
  font-size: 26rpx;
}

/* 联系方式弹窗 - 从下方弹出 */
.contact-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 999;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.contact-modal-show {
  opacity: 1;
  visibility: visible;
}

.contact-modal-content {
  width: 100%;
  background-color: rgb(240, 240, 240);
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  padding-bottom: env(safe-area-inset-bottom, 20rpx);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.contact-modal-show .contact-modal-content {
  transform: translateY(0);
}

.contact-modal-header {
  position: relative;
  text-align: center;
  padding: 30rpx 20rpx 20rpx;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
}

.contact-modal-title {
  font-size: 45rpx;
  color: rgb(0, 0, 0);
  font-weight: bold;
}

.contact-modal-close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.651);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-input-container {
  margin: 20rpx 50rpx;
}

.contact-input {
  width: 100%;
  background-color: rgba(138, 116, 249, 0.15);
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 40rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
}

.contact-input::placeholder {
  color: rgba(138, 116, 249, 0.6);
  font-size: 30rpx;
}

.contact-modal-footer {
  padding: 30rpx 50rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom, 20rpx));
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-tip {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
  text-align: center;
}

.contact-confirm-btn {
  background-color: rgba(138, 116, 249, 1);
  color: white;
  padding: 25rpx 80rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(138, 116, 249, 0.3);
  min-width: 200rpx;
}

.contact-confirm-btn:active {
  transform: scale(0.96);
  opacity: 0.8;
}