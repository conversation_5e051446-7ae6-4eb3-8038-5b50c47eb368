// pages/foldshare/yijianpub/yijianpub.js
const { navigateBack } = require('../../../utils/navigation');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    qq: '',
    vx: '',
    phonenumber: '',
    contactresult: '未填写',
    showModal: false,
    selectedIndex: '0',
    showModal2: false,
    selectedType: '请选择',
    selected: '',
    selectedItem: {},
    showDropdown: false,
    images: [],
    content1: '',
    jine: '',
    isPublishing: false,
    weizhi: '',
    items: [
      { value: '102', name: '意见建议' }
    ],
    selected: '',
    isVoteEnabled: false,
    voteType: 'single',
    voteTitle: '',  // 添加投票标题
    voteOptions: ['', ''],  // 默认两个空选项
    minOptionsRequired: 2,  // 最少需要的选项数
    maxOptionsAllowed: 5,  // 最多允许的选项数
    isAnonymous: false, // 添加匿名状态
  },
  handleModalClose2() {
    this.setData({ showModal2: false });
  },
  dianji4: function (e) {
    const value = e.currentTarget.dataset.index; // 获取点击的头衔索引
    const name = e.currentTarget.dataset.name; // 获取头衔对象

    this.setData({
      selectedIndex: value, // 更新选中的索引
      selectedType: name,// 存储选中的touxian_name
      showModal2: !this.data.showModal2
    });
  },
  dianji3(e) {
    this.setData({
      showModal2: true,
    });
  },
  dianji5(e) {
    this.setData({
      showModal: true,
    });
  },
  radioChange(e) {
    const selectedValue = e.detail.value;
    const selectedItem = this.data.items.find(item => item.value === selectedValue);

    this.setData({
      selected: selectedValue,
      selectedItem: selectedItem,
      showDropdown: false
    });
  },
  onClickLeft() {
    navigateBack();
  },

  onClickRight() {
    wx.showToast({ title: '点击按钮', icon: 'none' });
  },
  onInputChange(event) {
    this.setData({
      jine: event.detail.value
    });
  },
  onInputChange2(event) {
    this.setData({
      weizhi: event.detail.value
    });
  },
  onInputChange3(event) {
    this.setData({
      vx: event.detail.value
    });
  },
  onInputChange4(event) {
    this.setData({
      qq: event.detail.value
    });
  },
  onInputChange5(event) {
    this.setData({
      phonenumber: event.detail.value
    });
  },
  onChange1(event) {
    this.setData({
      content1: event.detail.value
    });
  },
  previewImage(e) {
    const index = e.currentTarget.dataset.index; // 获取点击的图片索引
    wx.previewImage({
      current: this.data.images[index], // 当前预览的图片
      urls: this.data.images // 图片列表
    });
  },
  chooseImage() {
    const that = this;
    wx.chooseImage({
      count: 9 - this.data.images.length, // 限制选择的图片数量
      sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
      success(res) {
        const tempFilePaths = res.tempFilePaths;
        that.setData({
          images: that.data.images.concat(tempFilePaths) // 将新选择的图片加入数组
        });
      }
    });
  },

  // 投票相关函数
  toggleVote() {
    this.setData({
      isVoteEnabled: !this.data.isVoteEnabled
    });
  },

  handleVoteTitleInput(e) {
    this.setData({
      voteTitle: e.detail.value
    });
  },

  switchVoteType(e) {
    this.setData({
      voteType: e.currentTarget.dataset.type
    });
  },

  handleOptionInput(e) {
    const index = e.currentTarget.dataset.index;
    const newOptions = [...this.data.voteOptions];
    newOptions[index] = e.detail.value;
    this.setData({
      voteOptions: newOptions
    });
  },

  addOption() {
    if (this.data.voteOptions.length < this.data.maxOptionsAllowed) {
      this.setData({
        voteOptions: [...this.data.voteOptions, '']
      });
    }
  },

  deleteOption(e) {
    const index = e.currentTarget.dataset.index;
    if (this.data.voteOptions.length > this.data.minOptionsRequired) {
      const newOptions = this.data.voteOptions.filter((_, i) => i !== index);
      this.setData({
        voteOptions: newOptions
      });
    }
  },

  validateVoteOptions() {
    if (!this.data.voteTitle.trim()) {
      wx.showToast({
        title: '请输入投票标题',
        icon: 'none'
      });
      return false;
    }

    const validOptions = this.data.voteOptions.filter(option => option.trim());
    if (validOptions.length < 2) {
      wx.showToast({
        title: '至少需要两个有效选项',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  formatVoteData() {
    if (!this.data.isVoteEnabled) return null;

    return {
      title: this.data.voteTitle,
      type: this.data.voteType,
      options: this.data.voteOptions.filter(option => option.trim())
    };
  },

  toggleAnonymous() {
    this.setData({
      isAnonymous: !this.data.isAnonymous
    });
  },

  publish() {
    if (this.data.isPublishing) return;
    
    // 添加内容验证
    if (!this.data.content1 || this.data.content1.trim() === '') {
      wx.showToast({
        title: '请输入内容',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 验证用户ID
    const userId = wx.getStorageSync('user_id');
    if (!userId) {
      wx.showToast({
        title: '用户未登录',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    this.setData({ isPublishing: true });
    wx.showLoading({
      title: '正在上传...',
    });

    if (this.data.images.length > 0) {
      // 获取token用于图片上传
      const accessToken = wx.getStorageSync('access_token');
      if (!accessToken) {
        wx.hideLoading();
        this.setData({ isPublishing: false });
        wx.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      const uploadPromises = this.data.images.map(imagePath => {
        return new Promise((resolve, reject) => {
          if (!imagePath) {
            resolve(null);
            return;
          }
          wx.uploadFile({
            url: getApp().globalData.wangz + '/message/uploadImage',
            filePath: imagePath,
            name: 'file',
            header: {
              'token': accessToken
            },
            formData: {
              'user_id': userId
            },
            success(res) {
              try {
                const data = JSON.parse(res.data);
                if (data.error_code === 0) {
                  if (data.data && data.data.image_url) {
                    resolve(data.data.image_url);
                  } else {
                    resolve(data.image_url);
                  }
                } else {
                  console.error('上传失败:', data);
                  resolve(null);
                }
              } catch (e) {
                console.error('JSON 解析错误:', e.message);
                resolve(null);
              }
            },
            fail(err) {
              console.error('图片上传失败:', err);
              resolve(null);
            }
          });
        });
      });

      Promise.all(uploadPromises)
        .then(uploadedImages => {
          const validImages = uploadedImages.filter(img => img !== null);
          this.publishWithImages(validImages);
        })
        .catch(error => {
          console.error('发布错误:', error);
          wx.hideLoading();
          this.setData({ isPublishing: false });
          wx.showToast({
            title: '图片上传失败',
            icon: 'error',
            duration: 2000
          });
        });
    } else {
      this.publishWithImages([]);
    }
  },

  publishWithImages(uploadedImages) {
    // 从本地存储获取用户信息
    const userId = wx.getStorageSync('user_id');
    if (!userId) {
      wx.hideLoading();
      this.setData({ isPublishing: false });
      wx.showToast({
        title: '用户未登录',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 如果是匿名发布，使用占位符信息，后端会进行真正的匿名处理
    const username = this.data.isAnonymous ? 'anonymous_placeholder' : wx.getStorageSync('username');
    const faceUrl = this.data.isAnonymous ? 'anonymous_placeholder' : wx.getStorageSync('face_url');
    const titlename = this.data.isAnonymous ? '' : (wx.getStorageSync('titlename') || '无');
    const titlecolor = this.data.isAnonymous ? '0' : (wx.getStorageSync('titlecolor') || '0');

    const validImages = uploadedImages.filter(img => img !== null && img !== undefined);

    const requestData = {
      choose: '102',
      user_id: userId,
      username: username,
      face_url: faceUrl,
      content: this.data.content1,
      titlename: titlename,
      titlecolor: titlecolor,
      vx: this.data.vx || '',
      qq: this.data.qq || '',
      phonenumber: this.data.phonenumber || '',
      is_anonymous: this.data.isAnonymous ? 1 : 0
    };

    validImages.forEach((img, index) => {
      requestData[`images[${index}]`] = img;
    });

    console.log('发送的请求数据:', requestData); // 添加日志

    // 获取token
    const accessToken = wx.getStorageSync('access_token');
    if (!accessToken) {
      wx.hideLoading();
      this.setData({ isPublishing: false });
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.request({
      url: getApp().globalData.wangz + '/message/publish',
      method: 'POST',
      data: requestData,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': accessToken
      },
      success: (res) => {
        console.log('发布响应:', res.data); // 添加日志
        wx.hideLoading();
        this.setData({ isPublishing: false });

        if (res.data.error_code === 0) {
          wx.showToast({
            title: '发布成功',
            icon: 'success',
            duration: 1000
          });
          
          // 获取发布成功后的消息ID
          if (res.data.data && res.data.data.message_id) {
            // 发送模板消息通知
            this.sendTemplateMessage(res.data.data.message_id);
          }
          
          setTimeout(() => {
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];
            if (prevPage) {
              prevPage.setData({
                page: 1,
                messages: []
              });
              prevPage.loadMessages();
            }
            wx.navigateBack();
          }, 1000);
        } else {
          wx.showToast({
            title: res.data.msg || '发布失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (error) => {
        console.error('请求失败:', error); // 添加日志
        wx.hideLoading();
        this.setData({ isPublishing: false });
        wx.showToast({
          title: '发布失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 发送模板消息通知
  sendTemplateMessage(messageId) {
    const app = getApp();
    
    // 获取小程序用户的OpenID
    const miniProgramOpenId = wx.getStorageSync('openid');
    if (!miniProgramOpenId) {
      console.log('未找到用户OpenID，无法发送模板消息');
      return;
    }
    
    // 获取token
    const accessToken = wx.getStorageSync('access_token');
    if (!accessToken) {
      console.log('未找到token，无法发送模板消息');
      return;
    }

    // 使用小程序OpenID关联的方式发送模板消息
    wx.request({
      url: app.globalData.wangz + '/templateMessage/sendFeedbackByMiniProgramOpenId',
      method: 'POST',
      data: {
        miniprogram_openid: miniProgramOpenId,
        message_id: messageId,
        feedback_content: this.data.content1,
        template_id: 'NsNEKtbrL2NighG7hxolJKDzc1_gV2PgbcXF3gawXQw'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': accessToken
      },
      success: (res) => {
        console.log('模板消息发送结果:', res.data);
        
        // 如果返回错误码2，说明未找到映射关系，提示用户关注公众号
        if (res.data.error_code === 2) {
          wx.showModal({
            title: '提示',
            content: '请关注我们的公众号以接收通知',
            showCancel: false
          });
        }
      },
      fail: (error) => {
        console.error('模板消息发送失败:', error);
      }
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.loadFontFace({
      family: '阿里妈妈刀隶体 Regular',
      source: 'url("https://www.bjgaoxiaoshequ.store/阿里妈妈刀隶体/发布.woff2")'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  showModal() {
    this.setData({
      showModal: true
    });
  },
  
  hideModal() {
    const { vx, qq, phonenumber } = this.data;
    const allEmpty = !vx && !qq && !phonenumber;
    
    this.setData({
      showModal: false,
      contactresult: allEmpty ? '未填写' : '已填写✅'
    });
  },

  preventTap() {
    // 阻止点击穿透
  },

  // 阻止触摸穿透
  preventTouchMove() {
    return false;
  },

  // 阻止点击穿透
  stopPropagation() {
    // 阻止事件冒泡
  },
})