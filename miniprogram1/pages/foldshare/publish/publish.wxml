<van-nav-bar class="navsetting" fixed="true" placeholder="true" bind:click-left="onClickLeft">
  <text class="navtext" slot="right">发布</text>
  <view slot="left">
    <image src="/images/chexiao.png" style="width: 60rpx; height: 55rpx; margin-top: 30rpx;" />
  </view>
</van-nav-bar>
<view class="fixed-button2" bindtap="publish" style="{{isPublishing ? 'pointer-events:none;' : ''}}">
  <image class="custom-icon" src="/images/pub.jpeg"></image>
</view>
<view class="gradient-background3">
  <view wx:if="{{showModal2}}" class="modal-mask" bindtap="handleModalClose2" catchtouchmove="true"></view>
  <view wx:if="{{showModal}}" class="modal-mask" bindtap="handleModalClose" catchtouchmove="true"></view>
  <!-- 框 -->
  <view class="bgkuang">
    <view class="textarea-container">
      <textarea value="{{ value }}" placeholder="这一刻的想法...（第一行默认为标题）" bindinput="onChange1" style="width: 100%; padding:0 50rpx; box-sizing: border-box;" maxlength="2000" auto-height="true" />
    </view>
    <view class="upload-container">
      <block wx:for="{{images.length < 9 ? images.length + 1 : 9}}" wx:key="index">
        <view class="image-item">
          <!-- 已上传的图片 -->
          <image wx:if="{{index < images.length}}" src="{{images[index]}}" mode="aspectFill" class="image" bindtap="previewImage" data-index="{{index}}" />
          <!-- 添加图片的按钮 -->
          <view wx:elif="{{index == images.length && images.length < 9}}" class="add-icon" bindtap="chooseImage">
            <text style="font-size: 100rpx;">+</text>
          </view>
        </view>
      </block>
    </view>
  </view>
  <view class="profile-container2">
    <view class="label">发布类别</view>
    <!-- 使用 button 包裹 image -->
    <button bind:tap="dianji3" class="avatar-button">
      <text class="label2">{{selectedType}}</text>
      <image class="avatar" src="/images/xiangxiajiantou.png" mode="heightFix" />
    </button>
  </view>
  <view class="profile-container2">
    <view class="label">联系方式(选填)</view>
    <!-- 使用 button 包裹 image -->
    <button bind:tap="dianji5" class="avatar-button">
      <text class="label2">{{contactresult}}</text>
      <image class="avatar" src="/images/xiangxiajiantou.png" mode="heightFix" />
    </button>
  </view>
  <view class="profile-container2">
    <view class="label">说明(选填)</view>
    <form bindsubmit="formsubmit">
      <input type="text" placeholder="输入价格或者别的说明" name="nickname" class="input-right" bindinput="onInputChange" value="{{inputValue}}" />
    </form>
  </view>
  <view class="profile-container2">
    <view class="label">位置(选填)</view>
    <form bindsubmit="formsubmit">
      <input type="text" placeholder="输入会显示在名字下方" name="nickname" class="input-right" bindinput="onInputChange2" value="{{inputValue}}" />
    </form>
  </view>
  <!-- 匿名发布开关 -->
  <view class="profile-container2" wx:if="{{selectedIndex === '2' || selectedIndex === '4' || selectedIndex === '99'}}">
    <view class="label">匿名发布</view>
    <view class="ios-switch {{isAnonymousEnabled ? 'enabled' : ''}}" bindtap="toggleAnonymous">
      <view class="switch-handle"></view>
    </view>
  </view>
<view class="profile-container2">
    <view class="label">添加投票</view>
    <view class="ios-switch {{isVoteEnabled ? 'enabled' : ''}}" bindtap="toggleVote">
      <view class="switch-handle"></view>
    </view>
  </view>
  <!-- 投票选项面板 -->
  <view class="vote-panel {{isVoteEnabled ? 'vote-panel-show' : ''}}" wx:if="{{isVoteEnabled}}">
    <!-- 添加投票标题输入框 -->
    <view class="vote-option">
      <view class="vote-option-content">
        <input class="vote-input" 
               placeholder="投票标题"
               value="{{voteTitle}}"
               bindinput="handleVoteTitleInput"/>
      </view>
    </view>

    <view class="vote-header">
      <text class="vote-title">投票选项</text>
      <view class="vote-type-switch">
        <view class="{{voteType === 'single' ? 'active' : ''}}" bindtap="switchVoteType" data-type="single">单选</view>
        <view class="{{voteType === 'multiple' ? 'active' : ''}}" bindtap="switchVoteType" data-type="multiple">多选</view>
      </view>
    </view>

    <view class="vote-options">
      <view class="vote-option" wx:for="{{voteOptions}}" wx:key="index">
        <view class="vote-option-content">
          <input class="vote-input" 
                 placeholder="选项 {{index + 1}}" 
                 value="{{item}}"
                 data-index="{{index}}"
                 bindinput="handleOptionInput"/>
          <text class="delete-icon"
                wx:if="{{index > 1}}"
                bindtap="deleteOption"
                data-index="{{index}}">×</text>
        </view>
      </view>
    </view>

    <view class="add-option" bindtap="addOption" wx:if="{{voteOptions.length < 5}}">
      <text>+ 添加选项</text>
    </view>
  </view>
  <view style="height: 40rpx;"></view>
  <view class="modal2" hidden="{{!showModal2}}" style="height: {{modalHeight}}; position: fixed; bottom: 0; width: 100%; z-index: 999;">
    <view class="modal-header3">
      <view class="modal-header4">选择发布类别</view>
    </view>
    <view class="border4"></view>
    <scroll-view scroll-y="true" style="max-height: 60vh; overflow: auto;">
      <block wx:for="{{items}}" wx:key="value">
        <view wx:if="{{index === 0}}" class="profile-container2" data-index="{{index}}">
          <view class="label">{{item.name}}</view>
          <button class="avatar-button" data-name="{{item.name}}" data-index="{{item.value}}" bind:tap="dianji4">
            <image class="avatar" wx:if="{{selectedIndex === item.value}}" style="height: 35rpx; width: 35rpx;" src="/images/duigou.png" mode="aspectFit" />
          </button>
        </view>

        <view wx:elif="{{index === titles.length - 1}}" class="profile-container2" data-index="{{index}}">
          <view class="label">{{item.name}}</view>
          <button bind:tap="dianji4" class="avatar-button" data-name="{{item.name}}" data-index="{{item.value}}">
            <image class="avatar" wx:if="{{selectedIndex === item.value}}" style="height: 35rpx; width: 35rpx;" src="/images/duigou.png" mode="aspectFit" />
          </button>
        </view>

        <view wx:else class="profile-container2" data-index="{{index}}">
          <view class="label">{{item.name}}</view>
          <button bind:tap="dianji4" class="avatar-button" data-name="{{item.name}}" data-index="{{item.value}}">
            <image class="avatar" wx:if="{{selectedIndex === item.value}}" style="height: 35rpx; width: 35rpx;" src="/images/duigou.png" mode="aspectFit" />
          </button>
        </view>
      </block>
      <view style="height: 60rpx;"></view>
    </scroll-view>
    <view class="modal-close" bindtap="handleModalClose2">
      <image src="/images/guanbi.png" style="width: 30rpx; height: 30rpx;"></image>
    </view>
  </view>
  <!-- 联系方式弹窗 - 从下方弹出 -->
  <view class="contact-modal {{showModal ? 'contact-modal-show' : ''}}" catchtap="handleModalClose" catchtouchmove="preventTouchMove">
    <view class="contact-modal-content" catchtap="stopPropagation">
      <view class="contact-modal-header">
        <view class="contact-modal-title">填写联系方式</view>
        <view class="contact-modal-close" bindtap="handleModalClose">
          <image src="/images/guanbi.png" style="width: 30rpx; height: 30rpx;"></image>
        </view>
      </view>
      <view class="profile-container2">
        <view class="label">微信</view>
        <form bindsubmit="formsubmit">
          <input type="text" placeholder="填几个都行" name="nickname" class="input-right" bindinput="onInputChange3" value="{{vx}}" />
        </form>
      </view>
      <view class="profile-container2">
        <view class="label">QQ</view>
        <form bindsubmit="formsubmit">
          <input type="text" placeholder="请输入QQ" name="nickname" class="input-right" bindinput="onInputChange4" value="{{qq}}" />
        </form>
      </view>
      <view class="profile-container2">
        <view class="label">电话</view>
        <form bindsubmit="formsubmit">
          <input type="text" placeholder="请输入电话" name="nickname" class="input-right" bindinput="onInputChange5" value="{{phonenumber}}" />
        </form>
      </view>

      <!-- 发布按钮 -->
      <view class="contact-modal-footer">
        <view class="contact-publish-btn" bindtap="publish" style="{{isPublishing ? 'opacity:0.5;pointer-events:none;' : ''}}">
          确认发布
        </view>
      </view>
    </view>
  </view>

</view>

<!-- 发布时的遮罩 -->
<view class="publish-mask" wx:if="{{isPublishing}}"></view>
<!-- Loading组件 -->
<loading show="{{isPublishing}}" mask="{{false}}" />