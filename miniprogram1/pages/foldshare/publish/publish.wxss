/* pages/publish/publish.wxss */
.navsetting{
  --nav-bar-background-color: rgb(245, 245, 245);
}
.gradient-background3 {
  padding-top: 50rpx;
  min-height: 100vh; 
  height: auto;
  background-color: rgb(245, 245, 245);
}
.navtext{
  margin-bottom: 10rpx;
  font-family: '阿里妈妈刀隶体 Regular发布';
  font-size: 62rpx;
  margin-right: 287rpx;
}

.custom-icon{
  width: 80rpx;
 height: 80rpx;
 margin-right: 5px;
 border-radius: 50%;
}
.fixed-button2 {
  position: fixed;
  bottom: 200rpx;
  right: 20rpx;
  color: white;
  background-color:rgb(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  z-index: 1000;
}
.radio-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.radio-item {
  width: 30%; /* 宽度设置为父容器的 30% 使其在一行显示 3 个 */
  margin-bottom: 10px;
}
.textarea-container{
  margin-bottom: 80rpx;
}
.upload-container {
  display: flex;
  flex-wrap: wrap;
  padding-left: 40rpx; /* 左侧间隔 */
  padding-right: 40rpx; /* 右侧间隔 */
}

.image-item {
  width: calc(33.33% - 20rpx); /* 每个图片的宽度减去间隔的一半 */
  padding-bottom: calc(33.33% - 20rpx); /* 确保 1:1 的长宽比 */
  position: relative;
  margin-right: 20rpx; /* 图片之间的水平间隔 */
  margin-bottom: 20rpx; /* 图片之间的垂直间隔 */
  border-radius: 13rpx;
  overflow: hidden;
  box-sizing: border-box;
}

.image-item:nth-child(3n) {
  margin-right: 0; /* 每行最后一张图片不加水平间隔 */
}

.image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.placeholder {
  background-color: #f0f0f0;
}

.add-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: rgb(188, 188, 188);
  background-color: rgb(235, 235, 235);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 13rpx;
}
.dropdown {
  margin: 0 auto;
  position: relative;
  width: 90%;
}


.dropdown-bar {
  justify-content: center; /* 水平居中 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  padding: 10rpx;
  border-radius: 5rpx;
  background-color: rgb(245, 245, 245);
}

.dropdown-label {
  flex-basis: 25%;
  text-align: left;
}

.selected-item {
  flex-grow: 1;
  text-align: right;
  padding-right: 20rpx;
}

.dropdown-button {
  background: none;
  border: none;
  font-size: 30rpx;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: rgb(245, 245, 245);
  z-index: 10;
  border-radius: 5rpx;
}
.input-field {
  text-align: right;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx;
  border-radius: 5rpx;
  background-color: rgb(245, 245, 245);
}
.bgkuang{
  padding-top: 40rpx;
  padding-bottom: 20rpx;
  height: auto;
  background-color: rgb(255, 255, 255);
  margin-right: 40rpx;
  margin-left: 40rpx;
  border-radius:20rpx;
  margin-bottom: 40rpx;
}
.label {
  flex-shrink: 0; /* 靠左，不扩展 */
  font-size: 28rpx;
  color: #333;
}
.profile-container2 {
  height: 60rpx; /* 修改高度 */
  margin-left: 50rpx;
  margin-right: 50rpx;
  margin-top:15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  padding-right: 40rpx;
  padding-bottom: 20rpx;
  padding-left: 40rpx;
  background-color: #ffffff;
  border-top-left-radius: 20rpx; /* 左上角圆角 */
  border-top-right-radius: 20rpx; /* 右上角圆角 */
  border-bottom-left-radius: 20rpx; /* 左下角圆角 */
  border-bottom-right-radius: 20rpx; /* 右下角圆角 */
}
.input-right {
  font-size: 28rpx;
  text-align: right;
  color: #464141;
}
.avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 15%;
}
.avatar-button:active .avatar {
  transform: scale(1.1); /* 点击时头像放大 */
  transition: transform 0.2s;
}
.avatar-button{
  display: flex;
  justify-content: flex-end; /* 子组件靠右对齐 */
  align-items: center; /* 子组件垂直居中对齐 */
  flex-grow: 1; /* 占据剩余空间 */
  width: 60rpx; /* 按照头像的宽度设置 */
  height: 60rpx; /* 按照头像的高度设置 */
  border: none; /* 去掉按钮的默认边框 */
  padding: 0; /* 去掉按钮的默认内边距 */
  margin: 0;
  background-color: transparent; /* 红色背景 */
}
.avatar-button::after {
  border: none;
}
.label {
  flex-shrink: 0; /* 靠左，不扩展 */
  font-size: 28rpx;
  color: #333;
}
.label2 {
  font-size: 28rpx;
  color: #333;
}
.modal2 {
  z-index: 1001;
  box-sizing: border-box;
  padding-left: 20rpx;
  padding-right: 20rpx;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color:rgb(240, 240, 240);
  transition: height 0.3s;
  overflow: hidden;
  border-top-left-radius: 40rpx; /* 左上角圆角 */
  border-top-right-radius: 40rpx; /* 右上角圆角 */
}
.modal {
  padding-bottom: 50rpx;
  position: fixed;
  background-color:rgb(240, 240, 240);
  transition: height 0.3s;
  overflow: hidden;
  border-radius: 40rpx;
}
.modal-header3 {
  margin-top: 10rpx;
  text-align: center;
  border-top-left-radius: 40rpx; /* 左上角圆角 */
  border-top-right-radius: 40rpx; /* 右上角圆角 */
  height: 65rpx; /* 占全屏的15% */
  background-color: rgb(240, 240, 240); /* 可根据需要调整背景颜色 */
  padding: 20rpx;
}
.modal-header4 {
  text-align: center;
  background-color: rgb(240, 240, 240); /* 可根据需要调整背景颜色 */
  font-size: 45rpx; /* 设置字体大小 */
  color: rgb(0, 0, 0); /* 设置字体颜色 */
  font-weight: bold; /* 让字体加粗，接近黑体效果 */
  margin-bottom: 10rpx; /* 添加底部边距 */
}
.border{
  margin-top: 20rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
  margin-bottom: -10rpx;
  height: 2rpx;
  background-color: #ccc;
}
.modal-header5{
  margin-top: 20rpx;
  background-color: rgb(240, 240, 240); /* 可根据需要调整背景颜色 */
  color: rgb(43, 41, 41); /* 设置字体颜色 */
  font-size: 30rpx; /* 设置字体大小 */
}
.modal-close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  z-index: 1001;
  width: 60rpx; /* 宽度 */
  height: 60rpx; /* 高度 */
  background-color: rgba(255, 255, 255, 0.651); /* 按钮的背景颜色 */
  border-radius: 50%; /* 圆形 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3); /* 调整透明度 */
  z-index: 998; /* 比 modal 低一层 */
}
.vote-panel {
  background: #fff;
  margin: 15rpx 50rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  transform: translateY(-20rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.25, 1);
  height: 0;
  overflow: hidden;
  margin-bottom: 200rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.vote-panel-show {
  transform: translateY(0);
  opacity: 1;
  height: auto;
}

.vote-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}

.vote-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-left: 10rpx;
}

.vote-type-switch {
  display: flex;
  background: #f5f5f5;
  border-radius: 30rpx;
  padding: 4rpx;
  margin-right: 10rpx;
}

.vote-type-switch view {
  padding: 10rpx 30rpx;
  font-size: 26rpx;
  border-radius: 26rpx;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-tap-highlight-color: transparent;
}

.vote-type-switch view.active {
  background: #fff;
  color: #007aff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.vote-options {
  margin-bottom: 20rpx;
}

.vote-option {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.vote-option-content {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12rpx;
  padding: 20rpx;
  position: relative;
  transform: translateZ(0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.2s ease;
}

.vote-option-content:active {
  transform: scale(0.98);
  background: rgba(0, 0, 0, 0.05);
}

.vote-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding-left: 20rpx;
  padding-right: 50rpx;
  transition: background-color 0.3s ease;
}

.vote-input:focus {
  background: rgba(0,122,255,0.03);
}

.delete-icon {
  font-size: 40rpx;
  color: #999;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-tap-highlight-color: transparent;
  font-weight: 200;
  padding: 10rpx;
}

.delete-icon:active {
  color: #ff453a;
  transform: translateY(-50%) scale(0.8);
  opacity: 0.8;
}

.add-option {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  justify-content: center;
  position: relative;
  border-radius: 12rpx;
  transition: opacity 0.2s ease;
  -webkit-tap-highlight-color: transparent;
}

.add-option:active {
  opacity: 0.6;
}

.add-option text {
  font-size: 28rpx;
  color: #007aff;
  position: relative;
  z-index: 1;
}

.ios-switch {
  position: relative;
  width: 82rpx;
  height: 48rpx;
  background-color: #e9e9eb;
  border-radius: 24rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  -webkit-tap-highlight-color: transparent;
  transform: translateZ(0);
}

.ios-switch.enabled {
  background-color: #34c759;
  box-shadow: 0 2rpx 8rpx rgba(52,199,89,0.2);
}

.ios-switch:active {
  transform: scale(0.96);
}

.switch-handle {
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  width: 44rpx;
  height: 44rpx;
  background-color: #ffffff;
  border-radius: 22rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  will-change: transform, width;
}

/* 发布页面专用遮罩 */
.publish-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9998;
}

.ios-switch.enabled .switch-handle {
  transform: translateX(34rpx);
}

.ios-switch:active .switch-handle {
  width: 48rpx;
}

.ios-switch.enabled:active .switch-handle {
  width: 48rpx;
  transform: translateX(30rpx);
}

.vote-type-switch view:active {
  opacity: 0.7;
  transform: scale(0.96);
}

